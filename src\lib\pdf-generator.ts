'use client';

import { PDFDocument, PDFPage, PDFFont, StandardFonts, rgb, degrees } from 'pdf-lib';
import { CertificateTemplate, CertificateData } from '@/types/certificate';
import { downloadFile } from '@/lib/utils';
import { measurePerformance } from '@/components/common/PerformanceMonitor';
import { FontLoader } from '@/lib/font-loader';
import { UnifiedFontManager } from './unified-font-manager';
import { RenderingConsistencyManager } from './rendering-consistency-manager';
import { getPdfFontKey } from '@/lib/fonts';

/**
 * PDF生成器类
 * 负责将证书模板和数据转换为高质量的PDF文件
 */
export class PDFGenerator {
  private template: CertificateTemplate;
  private data: CertificateData;
  private pdfDoc: PDFDocument | null = null;
  private page: PDFPage | null = null;
  private fonts: { [key: string]: PDFFont } = {};
  private fontManager: UnifiedFontManager;
  private renderingManager: RenderingConsistencyManager;

  constructor(template: CertificateTemplate, data: CertificateData) {
    this.template = template;
    this.data = data;
    this.fontManager = UnifiedFontManager.getInstance();
    this.renderingManager = new RenderingConsistencyManager(template);
  }

  /**
   * 生成PDF并返回字节数组
   */
  async generate(): Promise<Uint8Array> {
    const startTime = performance.now();
    measurePerformance.mark('pdf-generation-start');

    try {
      // 创建PDF文档
      this.pdfDoc = await PDFDocument.create();
      
      // 设置文档元数据
      this.setDocumentMetadata();
      
      // 根据模板方向添加页面
      const pageSize = this.getPageSize();
      this.page = this.pdfDoc.addPage(pageSize);
      
      // 加载字体
      await this.loadFonts();
      
      // 绘制证书内容
      await this.drawCertificate();

      // 保存前分析PDF文档
      await this.analyzePDFDocument();

      // 返回PDF字节数组
      const pdfBytes = await this.pdfDoc.save();

      // 分析生成的PDF文件
      await this.analyzePDFFile(pdfBytes);

      // 性能监控
      measurePerformance.mark('pdf-generation-end');
      const endTime = performance.now();
      measurePerformance.measurePDFGeneration(startTime, endTime);
      measurePerformance.measure('pdf-generation-total', 'pdf-generation-start', 'pdf-generation-end');

      return pdfBytes;
    } catch (error) {
      console.error('PDF generation error:', error);
      throw new Error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 生成PDF并自动下载
   */
  async generateAndDownload(): Promise<void> {
    const pdfBytes = await this.generate();
    const blob = new Blob([pdfBytes], { type: 'application/pdf' });
    const filename = `certificate-${this.data.recipientName.replace(/[^a-zA-Z0-9]/g, '_')}-${Date.now()}.pdf`;
    downloadFile(blob, filename);
  }



  /**
   * 设置PDF文档元数据
   */
  private setDocumentMetadata(): void {
    if (!this.pdfDoc) return;

    const certificateTitle = 'Certificate';
    this.pdfDoc.setTitle(`${certificateTitle} - ${this.data.recipientName}`);
    this.pdfDoc.setSubject(certificateTitle);
    this.pdfDoc.setAuthor('Certificate Maker');
    this.pdfDoc.setCreator('Certificate Maker - https://certificate-maker.com');
    this.pdfDoc.setProducer('PDF-lib');
    this.pdfDoc.setCreationDate(new Date());
    this.pdfDoc.setModificationDate(new Date());
  }

  /**
   * 根据模板方向获取页面尺寸
   */
  private getPageSize(): [number, number] {
    // A4尺寸: 595.28 x 841.89 points
    const A4_WIDTH = 595.28;
    const A4_HEIGHT = 841.89;

    if (this.template.orientation === 'landscape') {
      // 横向：宽度 > 高度
      return [A4_HEIGHT, A4_WIDTH]; // 841.89 x 595.28
    } else {
      // 竖向：高度 > 宽度
      return [A4_WIDTH, A4_HEIGHT]; // 595.28 x 841.89
    }
  }

  /**
   * 加载所需字体 - 使用统一字体管理器的增强版本
   */
  private async loadFonts(): Promise<void> {
    if (!this.pdfDoc) return;

    console.log('🔤 开始初始化字体系统（统一管理器版本）...');

    try {
      // 加载标准字体
      console.log('📚 加载标准字体...');
      this.fonts.helvetica = this.pdfDoc.embedStandardFont(StandardFonts.Helvetica);
      this.fonts.helveticaBold = this.pdfDoc.embedStandardFont(StandardFonts.HelveticaBold);
      this.fonts.timesRoman = this.pdfDoc.embedStandardFont(StandardFonts.TimesRoman);
      this.fonts.timesRomanBold = this.pdfDoc.embedStandardFont(StandardFonts.TimesRomanBold);
      this.fonts.courier = this.pdfDoc.embedStandardFont(StandardFonts.Courier);
      this.fonts.courierBold = this.pdfDoc.embedStandardFont(StandardFonts.CourierBold);

      console.log('✅ 标准字体加载完成:', Object.keys(this.fonts).length);

      // 使用统一字体管理器预加载常用字体
      console.log('🚀 使用统一字体管理器加载自定义字体...');
      try {
        await this.fontManager.preloadCommonFonts(this.pdfDoc);

        // 加载关键字体并添加到本地字体映射
        const criticalFonts = [
          { family: 'Dancing Script', weight: 400 },
          { family: 'Playfair Display', weight: 400 },
          { family: 'Playfair Display', weight: 600 },
          { family: 'Inter', weight: 400 },
          { family: 'Crimson Text', weight: 400 },
          { family: 'Source Sans Pro', weight: 400 },
          { family: 'Great Vibes', weight: 400 }
        ];

        for (const { family, weight } of criticalFonts) {
          const result = await this.fontManager.loadFont(this.pdfDoc, family, weight);
          if (result.success && result.font) {
            const key = `${family}-${weight}`;
            this.fonts[key] = result.font;
            this.fonts[family] = result.font; // 也用简单名称作为键

            if (result.fallbackUsed) {
              console.log(`⚠️ ${family} 使用后备字体: ${result.font.name}`);
            } else {
              console.log(`✅ ${family} 加载成功: ${result.font.name}`);
            }
          } else {
            console.warn(`❌ ${family} 加载失败:`, result.error);
          }
        }

        console.log('🎉 统一字体管理器加载完成');
      } catch (error) {
        console.warn('❌ 统一字体管理器加载失败，使用后备字体:', error);
      }

      // 设置后备字体映射（保持兼容性）
      console.log('🔄 设置后备字体映射...');
      const fallbackMappings = [
        { key: 'Dancing Script', fallback: 'timesRoman', name: 'Times Roman' },
        { key: 'Playfair Display', fallback: 'timesRomanBold', name: 'Times Roman Bold' },
        { key: 'Inter', fallback: 'helvetica', name: 'Helvetica' },
        { key: 'Crimson Text', fallback: 'timesRoman', name: 'Times Roman' },
        { key: 'Source Sans Pro', fallback: 'helvetica', name: 'Helvetica' },
        { key: 'Great Vibes', fallback: 'timesRoman', name: 'Times Roman' }
      ];

      fallbackMappings.forEach(({ key, fallback, name }) => {
        if (!this.fonts[key]) {
          this.fonts[key] = this.fonts[fallback];
          console.log(`🔄 ${key} -> ${name} (后备字体)`);
        } else {
          console.log(`✅ ${key} (自定义字体)`);
        }
      });

      // 最终字体状态报告
      console.log('📊 最终字体映射状态:', {
        总字体数: Object.keys(this.fonts).length,
        Dancing_Script: this.fonts['Dancing Script'] ? '✅' : '❌',
        Dancing_Script_400: this.fonts['Dancing Script-400'] ? '✅' : '❌',
        所有字体: Object.keys(this.fonts)
      });

      // 深度验证关键字体
      await this.validateCriticalFonts();
    } catch (error) {
      console.error('Font loading error:', error);
      throw new Error('Failed to load fonts');
    }
  }

  /**
   * 验证关键字体的嵌入状态
   */
  private async validateCriticalFonts(): Promise<void> {
    console.log('🔍 开始验证关键字体...');

    const criticalFonts = [
      'Dancing Script',
      'Dancing Script-400',
      'Playfair Display',
      'Inter'
    ];

    for (const fontKey of criticalFonts) {
      const font = this.fonts[fontKey];
      if (font) {
        console.log(`✅ 关键字体验证通过: ${fontKey}`);

        // 测试字体基本功能
        try {
          const testText = '测试文字Test';
          const width = font.widthOfTextAtSize(testText, 16);
          const height = font.heightAtSize(16);

          console.log(`📏 字体度量测试: ${fontKey}`, {
            测试文字: testText,
            宽度: `${width.toFixed(2)}px`,
            高度: `${height.toFixed(2)}px`,
            字体名称: font.name || 'Unknown'
          });
        } catch (error) {
          console.error(`❌ 字体功能测试失败: ${fontKey}`, error);
        }
      } else {
        console.warn(`❌ 关键字体缺失: ${fontKey}`);
      }
    }

    // 特别验证Dancing Script字体
    const dancingScript = this.fonts['Dancing Script'] || this.fonts['Dancing Script-400'];
    if (dancingScript) {
      console.log('💃 Dancing Script字体最终验证:');
      console.log('  - 字体对象存在: ✅');
      console.log('  - 字体名称:', dancingScript.name || 'Unknown');
      console.log('  - 构造函数:', dancingScript.constructor.name);

      // 测试中文和英文渲染
      try {
        const chineseWidth = dancingScript.widthOfTextAtSize('张三', 24);
        const englishWidth = dancingScript.widthOfTextAtSize('John Doe', 24);
        console.log('  - 中文渲染测试: ✅', `"张三"@24px = ${chineseWidth.toFixed(2)}px`);
        console.log('  - 英文渲染测试: ✅', `"John Doe"@24px = ${englishWidth.toFixed(2)}px`);
      } catch (error) {
        console.error('  - 渲染测试失败: ❌', error);
      }
    } else {
      console.error('💃 Dancing Script字体验证失败: 字体对象不存在');
    }
  }

  /**
   * 分析PDF文档状态（保存前）
   */
  private async analyzePDFDocument(): Promise<void> {
    if (!this.pdfDoc) return;

    console.log('📊 PDF文档分析开始...');

    try {
      // 获取文档信息
      const pageCount = this.pdfDoc.getPageCount();
      const pages = this.pdfDoc.getPages();
      const firstPage = pages[0];
      const { width, height } = firstPage.getSize();

      console.log('📄 PDF文档基本信息:', {
        页面数量: pageCount,
        页面尺寸: `${width.toFixed(2)} x ${height.toFixed(2)}`,
        页面方向: width > height ? '横向' : '竖向'
      });

      // 分析嵌入的字体
      console.log('🔤 分析嵌入字体...');
      const embeddedFonts = Object.keys(this.fonts);
      console.log('📋 已嵌入字体列表:', embeddedFonts);

      // 检查关键字体
      const criticalFonts = ['Dancing Script', 'Dancing Script-400'];
      const missingCriticalFonts = criticalFonts.filter(font => !this.fonts[font]);

      if (missingCriticalFonts.length === 0) {
        console.log('✅ 所有关键字体已正确嵌入');
      } else {
        console.warn('⚠️ 缺失关键字体:', missingCriticalFonts);
      }

      // 验证Dancing Script字体
      const dancingScript = this.fonts['Dancing Script'] || this.fonts['Dancing Script-400'];
      if (dancingScript) {
        console.log('💃 Dancing Script字体验证:', {
          字体名称: dancingScript.name || 'Unknown',
          字体类型: dancingScript.constructor.name,
          可用方法: {
            widthOfTextAtSize: typeof dancingScript.widthOfTextAtSize === 'function',
            heightAtSize: typeof dancingScript.heightAtSize === 'function'
          }
        });
      }

    } catch (error) {
      console.error('❌ PDF文档分析失败:', error);
    }
  }

  /**
   * 分析生成的PDF文件（保存后）
   */
  private async analyzePDFFile(pdfBytes: Uint8Array): Promise<void> {
    console.log('🔍 PDF文件分析开始...');

    try {
      // 基本文件信息
      console.log('📊 PDF文件信息:', {
        文件大小: `${(pdfBytes.length / 1024).toFixed(2)} KB`,
        字节长度: pdfBytes.length,
        文件头: Array.from(pdfBytes.slice(0, 8)).map(b => String.fromCharCode(b)).join('')
      });

      // 尝试重新解析PDF以验证内容
      try {
        const parsedPdf = await PDFDocument.load(pdfBytes);
        const pageCount = parsedPdf.getPageCount();
        const pages = parsedPdf.getPages();
        const firstPage = pages[0];
        const { width, height } = firstPage.getSize();

        console.log('✅ PDF文件验证成功:', {
          页面数量: pageCount,
          页面尺寸: `${width.toFixed(2)} x ${height.toFixed(2)}`,
          文件完整性: '正常'
        });

        // 检查PDF内容（如果可能）
        console.log('📝 PDF内容验证完成');

      } catch (parseError) {
        console.error('❌ PDF文件解析失败:', parseError);
      }

    } catch (error) {
      console.error('❌ PDF文件分析失败:', error);
    }
  }

  /**
   * 绘制证书内容
   */
  private async drawCertificate(): Promise<void> {
    if (!this.page) return;

    // 绘制背景和边框
    await this.drawBackground();

    // 绘制装饰元素
    this.drawDecorations();

    // 绘制文本内容
    this.drawTexts();
  }

  /**
   * 绘制背景和边框
   */
  private async drawBackground(): Promise<void> {
    if (!this.page || !this.pdfDoc) return;

    const { width, height } = this.page.getSize();
    const colors = this.template.style.colors;

    // 如果有背景图片，绘制背景图片
    if (this.template.backgroundImage) {
      try {
        await this.drawBackgroundImage();
      } catch (error) {
        console.warn('Failed to load background image, falling back to color background:', error);
        // 如果背景图片加载失败，使用颜色背景
        this.drawColorBackground();
      }
    } else {
      // 绘制颜色背景
      this.drawColorBackground();
    }

    // 对于有背景图片的模板，不绘制边框（因为边框已经在图片中）
    if (!this.template.backgroundImage) {
      // 绘制外边框
      const borderWidth = this.getBorderWidth();
      this.page.drawRectangle({
        x: 20,
        y: 20,
        width: width - 40,
        height: height - 40,
        borderColor: this.hexToRgb(colors.primary),
        borderWidth: borderWidth,
      });

      // 绘制内边框
      this.page.drawRectangle({
        x: 30,
        y: 30,
        width: width - 60,
        height: height - 60,
        borderColor: this.hexToRgb(colors.primary),
        borderWidth: 1,
        opacity: 0.3,
      });
    }
  }

  /**
   * 绘制颜色背景
   */
  private drawColorBackground(): void {
    if (!this.page) return;

    const { width, height } = this.page.getSize();
    const colors = this.template.style.colors;

    this.page.drawRectangle({
      x: 0,
      y: 0,
      width,
      height,
      color: this.hexToRgb(colors.background),
    });
  }

  /**
   * 绘制背景图片
   */
  private async drawBackgroundImage(): Promise<void> {
    if (!this.page || !this.pdfDoc || !this.template.backgroundImage) return;

    const { width, height } = this.page.getSize();

    try {
      // 获取图片数据
      let imageUrl = this.template.backgroundImage;

      // 如果是相对路径，转换为完整URL
      if (imageUrl.startsWith('/')) {
        imageUrl = `${window.location.origin}${imageUrl}`;
      }

      console.log('Loading background image:', imageUrl);
      const response = await fetch(imageUrl);

      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
      }

      const imageBytes = await response.arrayBuffer();

      // 根据文件扩展名确定图片类型
      let image;
      if (imageUrl.toLowerCase().endsWith('.png')) {
        image = await this.pdfDoc.embedPng(imageBytes);
      } else if (imageUrl.toLowerCase().endsWith('.jpg') || imageUrl.toLowerCase().endsWith('.jpeg')) {
        image = await this.pdfDoc.embedJpg(imageBytes);
      } else {
        throw new Error(`Unsupported image format: ${imageUrl}`);
      }

      // 获取图片的原始尺寸
      const imageDims = image.scale(1);

      // 计算缩放比例以适应页面
      const scaleX = width / imageDims.width;
      const scaleY = height / imageDims.height;
      const scale = Math.min(scaleX, scaleY);

      // 计算居中位置
      const scaledWidth = imageDims.width * scale;
      const scaledHeight = imageDims.height * scale;
      const x = (width - scaledWidth) / 2;
      const y = (height - scaledHeight) / 2;

      // 绘制图片，保持宽高比并居中
      this.page.drawImage(image, {
        x,
        y,
        width: scaledWidth,
        height: scaledHeight,
      });

      console.log('Background image drawn successfully');
    } catch (error) {
      console.error('Error drawing background image:', error);
      throw error;
    }
  }

  /**
   * 绘制装饰元素
   */
  private drawDecorations(): void {
    if (!this.page) return;

    // 根据模板类型绘制不同的装饰
    switch (this.template.id) {
      case 'classic-business':
        this.drawClassicBusinessDecorations();
        break;
      case 'elegant-green':
        this.drawElegantGreenDecorations();
        break;
      case 'modern-purple':
        this.drawModernPurpleDecorations();
        break;
      case 'luxury-gold':
        this.drawLuxuryGoldDecorations();
        break;
    }

    // 移除通用装饰线 - 避免在completion-template-1中出现不需要的绿色横线
    // 如果需要装饰线，应该在具体的模板装饰方法中绘制
  }

  /**
   * 绘制文本内容
   */
  private drawTexts(): void {
    if (!this.page) return;

    // 不绘制标题 - 根据用户要求移除
    // this.drawTitle();

    // 绘制收件人姓名
    this.drawRecipientName();

    // 绘制详细信息
    this.drawDetails();

    // 绘制日期和签名
    this.drawDateAndSignature();
  }



  /**
   * 绘制收件人姓名 - 增强版本，包含详细的字体和坐标验证
   */
  private drawRecipientName(): void {
    if (!this.page) return;

    const { width, height } = this.page.getSize();
    const nameLayout = this.template.layout.name;

    console.log('👤 开始绘制收件人姓名...');
    console.log('📋 姓名字段配置:', {
      字体: nameLayout.fontFamily,
      权重: nameLayout.fontWeight || 600,
      大小: nameLayout.fontSize,
      颜色: nameLayout.color,
      对齐: nameLayout.align,
      模板坐标: `(${nameLayout.x}, ${nameLayout.y})`,
      尺寸: `${nameLayout.width}x${nameLayout.height}`
    });

    // 获取字体并验证
    const nameFont = this.getFont(nameLayout.fontFamily, nameLayout.fontWeight || 600);
    console.log('🔤 获取到的字体对象:', {
      字体名称: nameFont.name || 'Unknown',
      构造函数: nameFont.constructor.name,
      是否为Dancing_Script: nameFont === this.fonts['Dancing Script'] || nameFont === this.fonts['Dancing Script-400']
    });

    const nameSize = nameLayout.fontSize;
    const nameColor = this.hexToRgb(nameLayout.color);

    // 计算文字宽度和位置
    const textWidth = nameFont.widthOfTextAtSize(this.data.recipientName, nameSize);
    console.log('📏 文字度量:', {
      文字内容: this.data.recipientName,
      字体大小: nameSize,
      计算宽度: `${textWidth.toFixed(2)}px`
    });

    // 使用模板定义的位置和对齐方式
    let x = nameLayout.x;
    if (nameLayout.align === 'center') {
      x = nameLayout.x - textWidth / 2;
    } else if (nameLayout.align === 'right') {
      x = nameLayout.x - textWidth;
    }

    const finalY = height - nameLayout.y;

    console.log('📍 最终绘制坐标:', {
      PDF页面尺寸: `${width.toFixed(2)}x${height.toFixed(2)}`,
      模板Y坐标: nameLayout.y,
      PDF_Y坐标: finalY.toFixed(2),
      最终X坐标: x.toFixed(2),
      对齐方式: nameLayout.align
    });

    // 验证坐标合理性
    if (x < 0 || x > width || finalY < 0 || finalY > height) {
      console.warn('⚠️ 坐标超出页面范围!', {
        X坐标: x,
        Y坐标: finalY,
        页面范围: `0-${width} x 0-${height}`
      });
    }

    this.page.drawText(this.data.recipientName, {
      x,
      y: finalY,
      size: nameSize,
      font: nameFont,
      color: nameColor,
    });

    console.log('✅ 姓名绘制完成!', {
      绘制参数: {
        文字: this.data.recipientName,
        X坐标: x.toFixed(2),
        Y坐标: finalY.toFixed(2),
        字体大小: nameSize,
        字体对象: nameFont.name || nameFont.constructor.name,
        颜色: nameLayout.color
      }
    });
  }

  /**
   * 绘制详细信息
   */
  private drawDetails(): void {
    if (!this.page) return;

    const { height } = this.page.getSize();
    const detailsLayout = this.template.layout.details;
    const bodyFont = this.getFont(detailsLayout.fontFamily, detailsLayout.fontWeight || 400);
    const bodySize = detailsLayout.fontSize;
    const bodyColor = this.hexToRgb(detailsLayout.color);

    // 分割长文本为多行
    const maxWidth = detailsLayout.width;
    const lines = this.wrapText(this.data.details, bodyFont, bodySize, maxWidth);

    const lineHeight = bodySize * 1.5;
    const startY = height - detailsLayout.y;

    lines.forEach((line, index) => {
      let x = detailsLayout.x;
      if (detailsLayout.align === 'center') {
        const lineWidth = bodyFont.widthOfTextAtSize(line, bodySize);
        x = detailsLayout.x - lineWidth / 2;
      } else if (detailsLayout.align === 'right') {
        const lineWidth = bodyFont.widthOfTextAtSize(line, bodySize);
        x = detailsLayout.x - lineWidth;
      }

      this.page!.drawText(line, {
        x,
        y: startY - (index * lineHeight),
        size: bodySize,
        font: bodyFont,
        color: bodyColor,
      });
    });
  }

  /**
   * 绘制日期和签名
   */
  private drawDateAndSignature(): void {
    if (!this.page) return;

    const { height } = this.page.getSize();
    const dateLayout = this.template.layout.date;
    const signatureLayout = this.template.layout.signature;

    // 确保日期和签名数据存在
    if (!this.data.date || !this.data.signature) {
      console.warn('Missing date or signature data:', {
        date: this.data.date,
        signature: this.data.signature
      });
      return;
    }

    // 绘制日期
    if (this.data.date && this.data.date.trim()) {
      const dateFont = this.getFont(dateLayout.fontFamily, dateLayout.fontWeight || 400);
      const dateSize = dateLayout.fontSize;
      const dateColor = this.hexToRgb(dateLayout.color);

      let dateX = dateLayout.x;
      if (dateLayout.align === 'center') {
        const dateWidth = dateFont.widthOfTextAtSize(this.data.date, dateSize);
        dateX = dateLayout.x - dateWidth / 2;
      } else if (dateLayout.align === 'right') {
        const dateWidth = dateFont.widthOfTextAtSize(this.data.date, dateSize);
        dateX = dateLayout.x - dateWidth;
      }

      this.page.drawText(this.data.date, {
        x: dateX,
        y: height - dateLayout.y,
        size: dateSize,
        font: dateFont,
        color: dateColor,
      });
    }

    // 绘制签名
    if (this.data.signature && this.data.signature.trim()) {
      const signatureFont = this.getFont(signatureLayout.fontFamily, signatureLayout.fontWeight || 400);
      const signatureSize = signatureLayout.fontSize;
      const signatureColor = this.hexToRgb(signatureLayout.color);

      let signatureX = signatureLayout.x;
      if (signatureLayout.align === 'center') {
        const signatureWidth = signatureFont.widthOfTextAtSize(this.data.signature, signatureSize);
        signatureX = signatureLayout.x - signatureWidth / 2;
      } else if (signatureLayout.align === 'right') {
        const signatureWidth = signatureFont.widthOfTextAtSize(this.data.signature, signatureSize);
        signatureX = signatureLayout.x - signatureWidth;
      }

      this.page.drawText(this.data.signature, {
        x: signatureX,
        y: height - signatureLayout.y,
        size: signatureSize,
        font: signatureFont,
        color: signatureColor,
      });
    }
  }

  /**
   * 获取字体对象 - 使用统一字体管理器的增强版本
   */
  private getFont(family: string, weight: string | number): PDFFont {
    const numericWeight = typeof weight === 'string' ?
      (weight === 'bold' ? 700 : 400) : weight;

    console.log(`🔍 字体匹配开始: "${family}", 权重: ${numericWeight}`);
    console.log(`📋 当前可用字体列表:`, Object.keys(this.fonts));

    // 特别标记Dancing Script查找
    const isDancingScript = family.toLowerCase().includes('dancing script');
    if (isDancingScript) {
      console.log(`💃 检测到Dancing Script字体查找!`);
      console.log(`🔍 Dancing Script相关字体:`,
        Object.keys(this.fonts).filter(key => key.toLowerCase().includes('dancing')));
    }

    // 首先尝试精确匹配（包含权重）
    const exactKey = `${family}-${numericWeight}`;
    console.log(`🎯 尝试精确匹配: "${exactKey}"`);
    if (this.fonts[exactKey]) {
      console.log(`✅ 精确匹配成功: ${exactKey}`);
      if (isDancingScript) {
        console.log(`💃 Dancing Script精确匹配成功!`);
      }
      return this.fonts[exactKey];
    } else {
      console.log(`❌ 精确匹配失败: ${exactKey}`);
    }

    // 尝试简单名称匹配
    console.log(`🎯 尝试简单名称匹配: "${family}"`);
    if (this.fonts[family]) {
      console.log(`✅ 简单名称匹配成功: ${family}`);
      if (isDancingScript) {
        console.log(`💃 Dancing Script简单名称匹配成功!`);
      }
      return this.fonts[family];
    } else {
      console.log(`❌ 简单名称匹配失败: ${family}`);
    }

    // 尝试权重变体匹配
    console.log(`🔄 尝试权重变体匹配...`);
    const weightVariants = [400, 600, 700];
    for (const variant of weightVariants) {
      const variantKey = `${family}-${variant}`;
      console.log(`  🎯 检查变体: ${variantKey}`);
      if (this.fonts[variantKey]) {
        console.log(`✅ 权重变体匹配成功: ${variantKey}`);
        if (isDancingScript) {
          console.log(`💃 Dancing Script权重变体匹配成功!`);
        }
        return this.fonts[variantKey];
      }
    }
    console.log(`❌ 所有权重变体匹配失败`);

    // 特殊字体映射 - 优先使用自定义字体
    console.log(`🎯 进入特殊字体映射阶段...`);
    const familyLower = family.toLowerCase();

    if (familyLower.includes('dancing script')) {
      console.log(`💃 Dancing Script特殊映射开始...`);

      // 详细检查每个可能的Dancing Script字体
      const dancingScript400 = this.fonts['Dancing Script-400'];
      const dancingScript = this.fonts['Dancing Script'];
      const timesRoman = this.fonts.timesRoman;

      console.log(`🔍 Dancing Script字体检查:`, {
        'Dancing Script-400': dancingScript400 ? '✅存在' : '❌缺失',
        'Dancing Script': dancingScript ? '✅存在' : '❌缺失',
        'Times Roman (后备)': timesRoman ? '✅存在' : '❌缺失'
      });

      let selectedFont;
      let fontSource;

      if (dancingScript400) {
        selectedFont = dancingScript400;
        fontSource = 'Dancing Script-400 (首选)';
      } else if (dancingScript) {
        selectedFont = dancingScript;
        fontSource = 'Dancing Script (次选)';
      } else {
        selectedFont = timesRoman;
        fontSource = 'Times Roman (后备字体)';
      }

      console.log(`💃 Dancing Script最终选择: ${fontSource}`);
      console.log(`📊 选中字体信息:`, {
        name: selectedFont.name || 'Unknown',
        constructor: selectedFont.constructor.name
      });

      return selectedFont;
    }

    // 特殊字体映射 - 优先使用自定义字体
    console.log(`🎯 进入特殊字体映射阶段...`);
    const familyLower = family.toLowerCase();

    if (familyLower.includes('dancing script')) {
      console.log(`💃 Dancing Script特殊映射开始...`);

      // 详细检查每个可能的Dancing Script字体
      const dancingScript400 = this.fonts['Dancing Script-400'];
      const dancingScript = this.fonts['Dancing Script'];
      const timesRoman = this.fonts.timesRoman;

      console.log(`� Dancing Script字体检查:`, {
        'Dancing Script-400': dancingScript400 ? '✅存在' : '❌缺失',
        'Dancing Script': dancingScript ? '✅存在' : '❌缺失',
        'Times Roman (后备)': timesRoman ? '✅存在' : '❌缺失'
      });

      let selectedFont;
      let fontSource;

      if (dancingScript400) {
        selectedFont = dancingScript400;
        fontSource = 'Dancing Script-400 (首选)';
      } else if (dancingScript) {
        selectedFont = dancingScript;
        fontSource = 'Dancing Script (次选)';
      } else {
        selectedFont = timesRoman;
        fontSource = 'Times Roman (后备字体)';
      }

      console.log(`💃 Dancing Script最终选择: ${fontSource}`);
      console.log(`📊 选中字体信息:`, {
        字体名称: selectedFont.name || 'Unknown',
        构造函数: selectedFont.constructor.name,
        字体对象ID: selectedFont.toString().slice(0, 50) + '...'
      });

      return selectedFont;
    } else if (familyLower.includes('playfair')) {
      const isBold = numericWeight >= 600;
      const playfairWeight = isBold ? 600 : 400;
      const playfairFont = this.fonts[`Playfair Display-${playfairWeight}`] || this.fonts['Playfair Display'] ||
             (isBold ? this.fonts.timesRomanBold : this.fonts.timesRoman);
      console.log(`📚 Playfair Display映射结果: ${playfairWeight}权重`);
      return playfairFont;
    } else if (familyLower.includes('inter')) {
      const isBold = numericWeight >= 600;
      const interWeight = isBold ? 600 : 400;
      const interFont = this.fonts[`Inter-${interWeight}`] || this.fonts['Inter'] ||
             (isBold ? this.fonts.helveticaBold : this.fonts.helvetica);
      console.log(`🔤 Inter映射结果: ${interWeight}权重`);
      return interFont;
    } else if (familyLower.includes('crimson')) {
      const isBold = numericWeight >= 600;
      const crimsonWeight = isBold ? 600 : 400;
      const crimsonFont = this.fonts[`Crimson Text-${crimsonWeight}`] || this.fonts['Crimson Text'] ||
             (isBold ? this.fonts.timesRomanBold : this.fonts.timesRoman);
      console.log(`📖 Crimson Text映射结果: ${crimsonWeight}权重`);
      return crimsonFont;
    } else if (familyLower.includes('source sans')) {
      const isBold = numericWeight >= 600;
      const sourceWeight = isBold ? 600 : 400;
      const sourceFont = this.fonts[`Source Sans Pro-${sourceWeight}`] || this.fonts['Source Sans Pro'] ||
             (isBold ? this.fonts.helveticaBold : this.fonts.helvetica);
      console.log(`🔧 Source Sans Pro映射结果: ${sourceWeight}权重`);
      return sourceFont;
    } else if (familyLower.includes('great vibes')) {
      const greatVibesFont = this.fonts['Great Vibes-400'] || this.fonts['Great Vibes'] || this.fonts.timesRoman;
      console.log(`✨ Great Vibes映射结果`);
      return greatVibesFont;
    } else if (familyLower.includes('times')) {
      const isBold = numericWeight >= 600;
      const timesFont = isBold ? this.fonts.timesRomanBold : this.fonts.timesRoman;
      console.log(`📰 Times映射结果: ${isBold ? 'Bold' : 'Regular'}`);
      return timesFont;
    } else if (familyLower.includes('courier')) {
      const isBold = numericWeight >= 600;
      const courierFont = isBold ? this.fonts.courierBold : this.fonts.courier;
      console.log(`💻 Courier映射结果: ${isBold ? 'Bold' : 'Regular'}`);
      return courierFont;
    } else {
      const isBold = numericWeight >= 600;
      const defaultFont = isBold ? this.fonts.helveticaBold : this.fonts.helvetica;
      console.log(`⚠️ 使用默认字体: Helvetica ${isBold ? 'Bold' : 'Regular'}`);
      return defaultFont;
    }
  }

  /**
   * 将十六进制颜色转换为RGB
   */
  private hexToRgb(hex: string) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    if (!result) {
      return rgb(0, 0, 0);
    }
    return rgb(
      parseInt(result[1], 16) / 255,
      parseInt(result[2], 16) / 255,
      parseInt(result[3], 16) / 255
    );
  }

  /**
   * 获取边框宽度
   */
  private getBorderWidth(): number {
    const borderStyle = this.template.style.border;
    if (borderStyle.includes('3pt')) return 3;
    if (borderStyle.includes('2pt')) return 2;
    return 1;
  }

  /**
   * 文本换行处理
   */
  private wrapText(text: string, font: PDFFont, fontSize: number, maxWidth: number): string[] {
    const words = text.split(' ');
    const lines: string[] = [];
    let currentLine = '';

    for (const word of words) {
      const testLine = currentLine ? `${currentLine} ${word}` : word;
      const testWidth = font.widthOfTextAtSize(testLine, fontSize);

      if (testWidth <= maxWidth) {
        currentLine = testLine;
      } else {
        if (currentLine) {
          lines.push(currentLine);
          currentLine = word;
        } else {
          // 单词太长，强制换行
          lines.push(word);
        }
      }
    }

    if (currentLine) {
      lines.push(currentLine);
    }

    return lines;
  }

  /**
   * 绘制经典商务风格装饰
   */
  private drawClassicBusinessDecorations(): void {
    if (!this.page) return;

    const { width, height } = this.page.getSize();
    const primaryColor = this.hexToRgb(this.template.style.colors.primary);

    // 绘制四角装饰圆圈
    const circleRadius = 15;
    const margin = 50;

    // 左上角
    this.page.drawCircle({
      x: margin,
      y: height - margin,
      size: circleRadius,
      color: primaryColor,
      opacity: 0.1,
    });

    // 右上角
    this.page.drawCircle({
      x: width - margin,
      y: height - margin,
      size: circleRadius,
      color: primaryColor,
      opacity: 0.1,
    });

    // 左下角
    this.page.drawCircle({
      x: margin,
      y: margin,
      size: circleRadius,
      color: primaryColor,
      opacity: 0.1,
    });

    // 右下角
    this.page.drawCircle({
      x: width - margin,
      y: margin,
      size: circleRadius,
      color: primaryColor,
      opacity: 0.1,
    });
  }

  /**
   * 绘制优雅绿色风格装饰
   */
  private drawElegantGreenDecorations(): void {
    if (!this.page) return;

    const { width, height } = this.page.getSize();
    const secondaryColor = this.hexToRgb(this.template.style.colors.secondary);

    // 绘制顶部装饰线
    this.page.drawRectangle({
      x: width / 2 - 60,
      y: height - 80,
      width: 120,
      height: 3,
      color: secondaryColor,
      opacity: 0.2,
    });

    // 绘制底部装饰线
    this.page.drawRectangle({
      x: width / 2 - 60,
      y: 80,
      width: 120,
      height: 3,
      color: secondaryColor,
      opacity: 0.2,
    });
  }

  /**
   * 绘制现代紫色风格装饰
   */
  private drawModernPurpleDecorations(): void {
    if (!this.page) return;

    const { width, height } = this.page.getSize();
    const primaryColor = this.hexToRgb(this.template.style.colors.primary);

    // 绘制几何装饰 - 使用矩形代替三角形
    const rectSize = 15;
    const centerX = width / 2;
    const topY = height - 100;

    // 绘制菱形装饰
    this.page.drawRectangle({
      x: centerX - rectSize / 2,
      y: topY - rectSize / 2,
      width: rectSize,
      height: rectSize,
      color: primaryColor,
      opacity: 0.15,
      rotate: degrees(45),
    });
  }

  /**
   * 绘制奢华金色风格装饰
   */
  private drawLuxuryGoldDecorations(): void {
    if (!this.page) return;

    const { width, height } = this.page.getSize();
    const primaryColor = this.hexToRgb(this.template.style.colors.primary);

    // 绘制装饰边框
    const decorMargin = 40;
    this.page.drawRectangle({
      x: decorMargin,
      y: decorMargin,
      width: width - decorMargin * 2,
      height: height - decorMargin * 2,
      borderColor: primaryColor,
      borderWidth: 2,
      opacity: 0.3,
    });

    // 绘制内部装饰线
    const innerMargin = 50;
    this.page.drawLine({
      start: { x: innerMargin, y: height - innerMargin },
      end: { x: width - innerMargin, y: height - innerMargin },
      thickness: 1,
      color: primaryColor,
      opacity: 0.2,
    });

    this.page.drawLine({
      start: { x: innerMargin, y: innerMargin },
      end: { x: width - innerMargin, y: innerMargin },
      thickness: 1,
      color: primaryColor,
      opacity: 0.2,
    });
  }
}

/**
 * 便捷函数：生成PDF并下载
 */
export async function generateCertificatePDF(
  template: CertificateTemplate,
  data: CertificateData
): Promise<void> {
  const generator = new PDFGenerator(template, data);
  await generator.generateAndDownload();
}

/**
 * 便捷函数：生成PDF字节数组
 */
export async function generateCertificatePDFBytes(
  template: CertificateTemplate,
  data: CertificateData
): Promise<Uint8Array> {
  const generator = new PDFGenerator(template, data);
  return await generator.generate();
}
