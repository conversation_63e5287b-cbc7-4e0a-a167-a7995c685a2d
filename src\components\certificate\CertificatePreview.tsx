'use client';

import React from 'react';
import { CertificateData, CertificateTemplate } from '@/types/certificate';
import { getFontStyle } from '@/lib/fonts';
import { createCoordinateSystem, LayoutCalculator, ScaleCalculator, CoordinateDebugger } from '@/lib/coordinate-system';

interface CertificatePreviewProps {
  template: CertificateTemplate;
  formData: CertificateData;
  showCoordinates?: boolean;
  customScaleFactor?: number;
}



export default function CertificatePreview({
  template,
  formData,
  showCoordinates = false,
  customScaleFactor
}: CertificatePreviewProps) {
  // 根据模板方向确定预览容器的样式
  const previewContainerClass = template.orientation === 'landscape'
    ? 'aspect-[4/3] w-full max-w-2xl mx-auto' // 横向模板预览
    : 'aspect-[3/4] w-full max-w-md mx-auto';  // 竖向模板预览

  // 使用统一坐标系统进行计算
  const coordinateSystem = createCoordinateSystem(template.orientation);
  const scaleFactor = customScaleFactor || ScaleCalculator.getPresetScale(template.orientation);
  const layoutCalculator = new LayoutCalculator(coordinateSystem, scaleFactor);

  // 获取页面尺寸信息
  const getPageDimensions = () => {
    if (template.orientation === 'landscape') {
      return { width: 842, height: 595 };
    } else {
      return { width: 595, height: 842 };
    }
  };

  const pageDimensions = getPageDimensions();

  return (
    <div className="w-full">
      {/* <h3 className="text-lg font-semibold mb-4 text-gray-900">Preview</h3> */}
      <div className={`${previewContainerClass} relative`}>
        <div
          className="relative h-full w-full rounded-lg shadow-lg overflow-hidden"
          style={{
            backgroundColor: template.style.colors.background,
            fontFamily: template.style.fonts.body.family,
          }}
        >
          {/* 背景图片支持 */}
          {template.backgroundImage && (
            <div className="absolute inset-0">
              <img
                src={template.backgroundImage}
                alt="Certificate background"
                className="w-full h-full object-cover"
              />
            </div>
          )}
          {/* 边框装饰 */}
          {/* <div
            className="absolute inset-2 rounded border-2"
            style={{
              borderColor: template.style.colors.primary,
              borderWidth: template.style.border.includes('3pt') ? '3px' :
                          template.style.border.includes('2pt') ? '2px' : '1px',
            }}
          /> */}

          {/* 证书内容 - 使用绝对定位匹配PDF坐标 */}
          <div className="relative h-full w-full">

            {/* 收件人姓名 - 使用统一坐标系统确保精确匹配 */}
            {formData.recipientName && (() => {
              const pdfLayout = {
                x: template.layout.name.x,
                y: template.layout.name.y,
                width: template.layout.name.width,
                height: template.layout.name.height
              };
              const previewStyle = layoutCalculator.calculatePreviewStyle(pdfLayout);
              const textAlignment = layoutCalculator.calculateTextAlignment(template.layout.name.align);

              return (
                <div
                  className="absolute"
                  style={{
                    ...previewStyle,
                    ...textAlignment,
                    color: template.layout.name.color,
                    ...getFontStyle(
                      template.layout.name.fontFamily,
                      layoutCalculator.calculateFontSize(template.layout.name.fontSize),
                      template.layout.name.fontWeight
                    ),
                    display: 'flex',
                    alignItems: 'center',
                  lineHeight: '1.0',
                  letterSpacing: template.layout.name.fontFamily.includes('Dancing') ? '0.02em' : 'normal',
                  textRendering: 'optimizeLegibility',
                  WebkitFontSmoothing: 'antialiased',
                  MozOsxFontSmoothing: 'grayscale',
                }}
              >
                {formData.recipientName}
              </div>
              );
            })()}

            {/* 详细信息 - 使用统一坐标系统确保精确匹配 */}
            {formData.details && (() => {
              const pdfLayout = {
                x: template.layout.details.x,
                y: template.layout.details.y,
                width: template.layout.details.width,
                height: template.layout.details.height
              };
              const previewStyle = layoutCalculator.calculatePreviewStyle(pdfLayout);
              const textAlignment = layoutCalculator.calculateTextAlignment(template.layout.details.align);

              return (
                <div
                  className="absolute"
                  style={{
                    ...previewStyle,
                    ...textAlignment,
                    color: template.layout.details.color,
                    ...getFontStyle(
                      template.layout.details.fontFamily,
                      layoutCalculator.calculateFontSize(template.layout.details.fontSize),
                      template.layout.details.fontWeight
                    ),
                    display: 'flex',
                    alignItems: 'center',
                  lineHeight: '1.3',
                  textRendering: 'optimizeLegibility',
                  WebkitFontSmoothing: 'antialiased',
                  MozOsxFontSmoothing: 'grayscale',
                }}
              >
                {formData.details}
              </div>
              );
            })()}

            {/* 日期 - 使用统一坐标系统确保精确匹配 */}
            {formData.date && (() => {
              const pdfLayout = {
                x: template.layout.date.x,
                y: template.layout.date.y,
                width: template.layout.date.width,
                height: template.layout.date.height
              };
              const previewStyle = layoutCalculator.calculatePreviewStyle(pdfLayout);
              const textAlignment = layoutCalculator.calculateTextAlignment(template.layout.date.align);

              return (
                <div
                  className="absolute"
                  style={{
                    ...previewStyle,
                    ...textAlignment,
                    color: template.layout.date.color,
                    ...getFontStyle(
                      template.layout.date.fontFamily,
                      layoutCalculator.calculateFontSize(template.layout.date.fontSize),
                      template.layout.date.fontWeight
                    ),
                    display: 'flex',
                    alignItems: 'center',
                  lineHeight: '1.0',
                  textRendering: 'optimizeLegibility',
                  WebkitFontSmoothing: 'antialiased',
                  MozOsxFontSmoothing: 'grayscale',
                }}
              >
                {formData.date}
              </div>
              );
            })()}

            {/* 签名 - 使用统一坐标系统确保精确匹配 */}
            {formData.signature && (() => {
              const pdfLayout = {
                x: template.layout.signature.x,
                y: template.layout.signature.y,
                width: template.layout.signature.width,
                height: template.layout.signature.height
              };
              const previewStyle = layoutCalculator.calculatePreviewStyle(pdfLayout);
              const textAlignment = layoutCalculator.calculateTextAlignment(template.layout.signature.align);

              return (
                <div
                  className="absolute"
                  style={{
                    ...previewStyle,
                    ...textAlignment,
                    color: template.layout.signature.color,
                    ...getFontStyle(
                      template.layout.signature.fontFamily,
                      layoutCalculator.calculateFontSize(template.layout.signature.fontSize),
                      template.layout.signature.fontWeight
                    ),
                    display: 'flex',
                    alignItems: 'center',
                  lineHeight: '1.0',
                  letterSpacing: template.layout.signature.fontFamily.includes('Dancing') ||
                                template.layout.signature.fontFamily.includes('Great Vibes') ? '0.02em' : 'normal',
                  textRendering: 'optimizeLegibility',
                  WebkitFontSmoothing: 'antialiased',
                  MozOsxFontSmoothing: 'grayscale',
                }}
              >
                {formData.signature}
              </div>
              );
            })()}

            {/* 坐标调试信息 - 修正坐标计算 */}
            {showCoordinates && (
              <>
                {/* 姓名区域边框 */}
                {(() => {
                  const pdfLayout = {
                    x: template.layout.name.x,
                    y: template.layout.name.y,
                    width: template.layout.name.width,
                    height: template.layout.name.height
                  };
                  const previewStyle = layoutCalculator.calculatePreviewStyle(pdfLayout);

                  return (
                    <div
                      className="absolute border-2 border-red-500 bg-red-100 bg-opacity-30"
                      style={previewStyle}
                    >
                      <div className="text-xs text-red-600 bg-white px-1">
                        姓名 (PDF: x:{template.layout.name.x}, y:{template.layout.name.y})
                      </div>
                    </div>
                  );
                })()}

                {/* 详情区域边框 */}
                <div
                  className="absolute border-2 border-blue-500 bg-blue-100 bg-opacity-30"
                  style={{
                    left: `${template.layout.details.x * scaleFactor}px`,
                    top: `${template.layout.details.y * scaleFactor}px`,
                    width: `${template.layout.details.width * scaleFactor}px`,
                    height: `${template.layout.details.height * scaleFactor}px`,
                  }}
                >
                  <div className="text-xs text-blue-600 bg-white px-1">
                    详情 (x:{template.layout.details.x}, y:{template.layout.details.y})
                  </div>
                </div>

                {/* 日期区域边框 */}
                <div
                  className="absolute border-2 border-green-500 bg-green-100 bg-opacity-30"
                  style={{
                    left: `${template.layout.date.x * scaleFactor}px`,
                    top: `${template.layout.date.y * scaleFactor}px`,
                    width: `${template.layout.date.width * scaleFactor}px`,
                    height: `${template.layout.date.height * scaleFactor}px`,
                  }}
                >
                  <div className="text-xs text-green-600 bg-white px-1">
                    日期 (x:{template.layout.date.x}, y:{template.layout.date.y})
                  </div>
                </div>

                {/* 签名区域边框 */}
                <div
                  className="absolute border-2 border-purple-500 bg-purple-100 bg-opacity-30"
                  style={{
                    left: `${template.layout.signature.x * scaleFactor}px`,
                    top: `${template.layout.signature.y * scaleFactor}px`,
                    width: `${template.layout.signature.width * scaleFactor}px`,
                    height: `${template.layout.signature.height * scaleFactor}px`,
                  }}
                >
                  <div className="text-xs text-purple-600 bg-white px-1">
                    签名 (x:{template.layout.signature.x}, y:{template.layout.signature.y})
                  </div>
                </div>
              </>
            )}


          </div>

          {/* 装饰元素 */}
          {/* {template.id === 'classic-business' && (
            <>
              <div
                className="absolute top-4 left-4 w-8 h-8 rounded-full opacity-10"
                style={{ backgroundColor: template.style.colors.primary }}
              />
              <div
                className="absolute top-4 right-4 w-8 h-8 rounded-full opacity-10"
                style={{ backgroundColor: template.style.colors.primary }}
              />
              <div
                className="absolute bottom-4 left-4 w-8 h-8 rounded-full opacity-10"
                style={{ backgroundColor: template.style.colors.primary }}
              />
              <div
                className="absolute bottom-4 right-4 w-8 h-8 rounded-full opacity-10"
                style={{ backgroundColor: template.style.colors.primary }}
              />
            </>
          )} */}

          {/* 移除装饰元素，确保与PDF生成器一致 */}
          {template.id === 'elegant-green' && (
            <>
              <div
                className="absolute top-6 left-1/2 transform -translate-x-1/2 w-12 h-1 opacity-20"
                style={{ backgroundColor: template.style.colors.secondary }}
              />
              <div
                className="absolute bottom-6 left-1/2 transform -translate-x-1/2 w-12 h-1 opacity-20"
                style={{ backgroundColor: template.style.colors.secondary }}
              />
            </>
          )}
        </div>
      </div>

      {/* 预览说明和操作 */}

    </div>
  );
}
